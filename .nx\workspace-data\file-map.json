{"version": "6.0", "nxVersion": "21.3.11", "pathMappings": {"@aggie/ui-components": ["packages/ui-components/src/index.ts"], "@aggie/shared-types": ["packages/shared-types/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": ".env.example", "hash": "5235307383338365252"}, {"file": ".github/instructions/nx.instructions.md", "hash": "2587759535909921375"}, {"file": "<PERSON><PERSON><PERSON>", "hash": "2537226200020467983"}, {"file": "docs/INTEGRATIONS.md", "hash": "15264511742675681405"}, {"file": "README-MONOREPO.md", "hash": "14405170070264619214"}, {"file": ".giti<PERSON>re", "hash": "184019514917621119"}, {"file": "tsconfig.base.json", "hash": "1106845473121590863"}, {"file": "docs/ARCHITECTURE.md", "hash": "4606996749694590355"}, {"file": "README.md", "hash": "7340930337580478741"}, {"file": "public/assets/aggi-nobg.png", "hash": "6387842823088557556"}, {"file": "DOCKER_MEMORY_FIX.md", "hash": "6613147478466856321"}, {"file": "start.md", "hash": "16037747602204937039"}, {"file": "docs/API.md", "hash": "7220424936992318662"}, {"file": "docker-compose.prod.yml", "hash": "4987633364360680466"}, {"file": "public/assets/aggi.png", "hash": "5793008265603454378"}, {"file": "check_invoices.py", "hash": "13851834429373149674"}, {"file": "docs/DEPLOYMENT.md", "hash": "867446732787071714"}, {"file": "docs/DEVELOPMENT.md", "hash": "9208299406763700867"}, {"file": "package.json", "hash": "16003416251037700812"}, {"file": "start-dev.sh", "hash": "3956233960854521037"}, {"file": "docker-compose.dev-safe.yml", "hash": "12868380772501782958"}, {"file": "nx.json", "hash": "7943974361133357733"}, {"file": "start-dev.bat", "hash": "5834417353735313207"}, {"file": "docker-compose.yml", "hash": "2759507992636017396"}, {"file": "nginx.conf", "hash": "1965059232790464839"}, {"file": "public/assets/aggi-smaller.png", "hash": "6402694523006771576"}, {"file": "package-lock.json", "hash": "6206680188960919387"}, {"file": "public/assets/aggi-small.png", "hash": "3106599298457136916"}, {"file": "start-dev.ps1", "hash": "5030113337551812369"}, {"file": "public/assets/aggi-100.png", "hash": "13887724856648226298"}, {"file": "init-db.sql", "hash": "6692502442178837968"}, {"file": "docker-compose.dev.yml", "hash": "17252756245022590705"}], "projectFileMap": {"ui-components": [{"file": "packages/ui-components/package.json", "hash": "551472291336843318", "deps": ["npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:@types/react", "npm:@types/react-dom", "npm:typescript", "npm:@headlessui/react", "npm:@heroicons/react"]}, {"file": "packages/ui-components/project.json", "hash": "11983930012854116052"}, {"file": "packages/ui-components/src/components/index.ts", "hash": "1145998184831838660"}, {"file": "packages/ui-components/src/components/utils.js", "hash": "574870991431321524"}, {"file": "packages/ui-components/src/index.ts", "hash": "805708636791503367"}, {"file": "packages/ui-components/tsconfig.lib.json", "hash": "14481697337928466754"}], "admin-frontend": [{"file": "apps/admin-frontend/Dockerfile", "hash": "16887350732645855918"}, {"file": "apps/admin-frontend/Dockerfile.prod", "hash": "8581775582639739374"}, {"file": "apps/admin-frontend/nginx.conf", "hash": "2486793215894162773"}, {"file": "apps/admin-frontend/package-lock.json", "hash": "7684299458499068964"}, {"file": "apps/admin-frontend/package.json", "hash": "6307208812747360930", "deps": ["npm:@types/qrcode.react", "ui-components", "shared-types", "npm:@types/node@20.19.9", "npm:@types/react", "npm:@types/react-dom", "npm:react", "npm:react-dom", "npm:react-router-dom", "npm:react-scripts", "npm:typescript@4.9.5", "npm:axios", "npm:react-hook-form", "npm:react-query", "npm:@headlessui/react", "npm:@heroicons/react", "npm:tailwindcss", "npm:autoprefixer", "npm:postcss", "npm:qrcode.react", "npm:react-hot-toast", "npm:@testing-library/jest-dom", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:@tailwindcss/forms"]}, {"file": "apps/admin-frontend/postcss.config.js", "hash": "1579725330904862112"}, {"file": "apps/admin-frontend/project.json", "hash": "17560911505815870571"}, {"file": "apps/admin-frontend/public/index.html", "hash": "17879939718833079058"}, {"file": "apps/admin-frontend/src/App.tsx", "hash": "4464613650970339519", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/SimpleApp.tsx", "hash": "4237033659271321490", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/__tests__/AuthContext.test.tsx", "hash": "12040871057496173520", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/__tests__/LoginPage.test.tsx", "hash": "13350870590789170105", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/components/Layout.tsx", "hash": "6931554571059759077", "deps": ["npm:react@18.3.1", "npm:react-router-dom", "npm:@headlessui/react", "npm:@heroicons/react"]}, {"file": "apps/admin-frontend/src/contexts/AuthContext.tsx", "hash": "9722001446352333396", "deps": ["npm:react@18.3.1", "npm:react-hot-toast"]}, {"file": "apps/admin-frontend/src/contexts/TenantContext.tsx", "hash": "9708045447128908387", "deps": ["npm:react@18.3.1"]}, {"file": "apps/admin-frontend/src/index.css", "hash": "6060890077207927115"}, {"file": "apps/admin-frontend/src/index.tsx", "hash": "12967885175928185568", "deps": ["npm:react", "npm:react-dom"]}, {"file": "apps/admin-frontend/src/pages/ActionItemsPage.tsx", "hash": "15426964114520051739", "deps": ["npm:react@18.3.1", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/admin-frontend/src/pages/DashboardPage.tsx", "hash": "10474820234636222236", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/pages/InvoicesPage.tsx", "hash": "1546253892446018728", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/admin-frontend/src/pages/LoginPage.tsx", "hash": "11449201771330526501", "deps": ["npm:react@18.3.1", "npm:react-router-dom", "npm:react-hook-form", "npm:@heroicons/react"]}, {"file": "apps/admin-frontend/src/pages/SettingsPage.tsx", "hash": "8480423347325354588", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/pages/TwoFAVerifyPage.tsx", "hash": "17330429575126296014", "deps": ["npm:react"]}, {"file": "apps/admin-frontend/src/services/api.ts", "hash": "13387638604291550987", "deps": ["npm:axios"]}, {"file": "apps/admin-frontend/src/setupTests.ts", "hash": "16903885983588411080"}, {"file": "apps/admin-frontend/src/types/index.ts", "hash": "8252561017891032678"}, {"file": "apps/admin-frontend/tailwind.config.js", "hash": "12413894931608446202"}, {"file": "apps/admin-frontend/tsconfig.app.json", "hash": "17310363877181359515"}, {"file": "apps/admin-frontend/tsconfig.json", "hash": "6764713495435129772"}], "shared-types": [{"file": "packages/shared-types/package.json", "hash": "17293241752369983537", "deps": ["npm:typescript"]}, {"file": "packages/shared-types/project.json", "hash": "4208504630303651189"}, {"file": "packages/shared-types/src/index.ts", "hash": "10460902349433359965"}, {"file": "packages/shared-types/src/types/index.ts", "hash": "2521834620632379610"}, {"file": "packages/shared-types/tsconfig.lib.json", "hash": "14481697337928466754"}], "backend-api": [{"file": "apps/backend-api/DOCKER_REBUILD_INSTRUCTIONS.md", "hash": "9222389678033392367"}, {"file": "apps/backend-api/Dockerfile", "hash": "16665707011653986344"}, {"file": "apps/backend-api/Dockerfile.prod", "hash": "5857787804534014413"}, {"file": "apps/backend-api/PACKAGE_CHANGES.md", "hash": "9387292605814467274"}, {"file": "apps/backend-api/alembic/env.py", "hash": "2630122021204022997"}, {"file": "apps/backend-api/alembic/script.py.mako", "hash": "10069961326131889856"}, {"file": "apps/backend-api/alembic/versions/001_install_pgvector.py", "hash": "9028612211006308560"}, {"file": "apps/backend-api/alembic/versions/002_create_tables.py", "hash": "4992486699753754871"}, {"file": "apps/backend-api/alembic/versions/003_enable_rls.py", "hash": "15191746906346635470"}, {"file": "apps/backend-api/alembic/versions/004_seed_roles.py", "hash": "7450986320602097145"}, {"file": "apps/backend-api/alembic/versions/005_add_foreign_keys.py", "hash": "139565071511276848"}, {"file": "apps/backend-api/alembic/versions/006_update_role_permissions.py", "hash": "1170032471955284379"}, {"file": "apps/backend-api/alembic/versions/007_add_invoice_integrations.py", "hash": "14932377457123667242"}, {"file": "apps/backend-api/alembic/versions/008_fix_encryption_configuration.py", "hash": "7992255627680506041"}, {"file": "apps/backend-api/alembic/versions/009_temporarily_disable_encryption.py", "hash": "16730165890753008677"}, {"file": "apps/backend-api/alembic/versions/010_enable_proper_encryption.py", "hash": "4881726145486818994"}, {"file": "apps/backend-api/alembic/versions/011_create_new_invoice_session_system.py", "hash": "12525440732854071870"}, {"file": "apps/backend-api/alembic/versions/012_enable_rls_for_sessions.py", "hash": "9332407984932560279"}, {"file": "apps/backend-api/alembic/versions/013_add_token_tracking.py", "hash": "12617804401787519948"}, {"file": "apps/backend-api/alembic/versions/014_add_tenant_fields.py", "hash": "6567035893172818939"}, {"file": "apps/backend-api/alembic/versions/015_add_session_id_to_action_items.py", "hash": "15030416598156630430"}, {"file": "apps/backend-api/app/__init__.py", "hash": "8530932077689354787"}, {"file": "apps/backend-api/app/api/v1/invoice_processing.py", "hash": "15838392346077946493"}, {"file": "apps/backend-api/app/celery_app.py", "hash": "14053655394664449070"}, {"file": "apps/backend-api/app/config.py", "hash": "2577892940849042652"}, {"file": "apps/backend-api/app/database.py", "hash": "13469041516490770634"}, {"file": "apps/backend-api/app/dependencies/auth.py", "hash": "9742078655975308414"}, {"file": "apps/backend-api/app/integrations/__init__.py", "hash": "2249975506754415871"}, {"file": "apps/backend-api/app/integrations/base.py", "hash": "14038749171068650457"}, {"file": "apps/backend-api/app/integrations/fortnox.py", "hash": "7539434295574265860"}, {"file": "apps/backend-api/app/integrations/simple_http.py", "hash": "1471125474146439864"}, {"file": "apps/backend-api/app/integrations/visma.py", "hash": "13888545133839108741"}, {"file": "apps/backend-api/app/main.py", "hash": "13821821051313630810"}, {"file": "apps/backend-api/app/middleware.py", "hash": "14753139604369767208"}, {"file": "apps/backend-api/app/models/__init__.py", "hash": "3120777853937660370"}, {"file": "apps/backend-api/app/models/action_item.py", "hash": "12772180686927015095"}, {"file": "apps/backend-api/app/models/base.py", "hash": "8875998508862954594"}, {"file": "apps/backend-api/app/models/integration.py", "hash": "7353828091350235831"}, {"file": "apps/backend-api/app/models/invoice.py", "hash": "16999688208678263593"}, {"file": "apps/backend-api/app/models/tenant.py", "hash": "18275832172368328746"}, {"file": "apps/backend-api/app/models/user.py", "hash": "13708580356275322886"}, {"file": "apps/backend-api/app/routers/__init__.py", "hash": "8173539746521203630"}, {"file": "apps/backend-api/app/routers/action_items.py", "hash": "16809745355142766140"}, {"file": "apps/backend-api/app/routers/auth.py", "hash": "17322900718569346911"}, {"file": "apps/backend-api/app/routers/base.py", "hash": "4228128290710873039"}, {"file": "apps/backend-api/app/routers/integrations.py", "hash": "11638125097714208399"}, {"file": "apps/backend-api/app/routers/invoices.py", "hash": "12974235032815257629"}, {"file": "apps/backend-api/app/routers/tenant.py", "hash": "8156510992188614152"}, {"file": "apps/backend-api/app/routers/users.py", "hash": "9383155649041114444"}, {"file": "apps/backend-api/app/schemas/__init__.py", "hash": "15514115020401103513"}, {"file": "apps/backend-api/app/schemas/action_item.py", "hash": "6690672318472202198"}, {"file": "apps/backend-api/app/schemas/auth.py", "hash": "5578502436228294068"}, {"file": "apps/backend-api/app/schemas/base.py", "hash": "6826673066351678352"}, {"file": "apps/backend-api/app/schemas/integration.py", "hash": "17800270703940947164"}, {"file": "apps/backend-api/app/schemas/invoice_processing.py", "hash": "171853101263838376"}, {"file": "apps/backend-api/app/schemas/tenant.py", "hash": "11656120322031516435"}, {"file": "apps/backend-api/app/schemas/user_example.py", "hash": "8827497842783416691"}, {"file": "apps/backend-api/app/services/__init__.py", "hash": "18032459416124082886"}, {"file": "apps/backend-api/app/services/integration_service.py", "hash": "2013055516849726318"}, {"file": "apps/backend-api/app/services/invoice_processing_service.py", "hash": "9899170376547892884"}, {"file": "apps/backend-api/app/services/llm_provider.py", "hash": "14736358721776277440"}, {"file": "apps/backend-api/app/services/ocr_service.py", "hash": "5507915056820774021"}, {"file": "apps/backend-api/app/services/prompt_service.py", "hash": "16763903359273091532"}, {"file": "apps/backend-api/app/services/session_log_service.py", "hash": "8456604177728771338"}, {"file": "apps/backend-api/app/services/vector_service.py", "hash": "6260963136192480261"}, {"file": "apps/backend-api/app/tasks/__init__.py", "hash": "8480902079378511937"}, {"file": "apps/backend-api/app/tasks/erp_integration.py", "hash": "5934102332196093423"}, {"file": "apps/backend-api/app/tasks/invoice_processing_tasks.py", "hash": "11700092406693096576"}, {"file": "apps/backend-api/app/tasks/notifications.py", "hash": "3572232295763718439"}, {"file": "apps/backend-api/app/utils/__init__.py", "hash": "6648062285855471645"}, {"file": "apps/backend-api/app/utils/email_validator.py", "hash": "1948687087061748247"}, {"file": "apps/backend-api/app/utils/encryption.py", "hash": "7658299233970602102"}, {"file": "apps/backend-api/app/utils/oauth2.py", "hash": "13249036402726177201"}, {"file": "apps/backend-api/app/utils/permissions.py", "hash": "16296545510626550645"}, {"file": "apps/backend-api/app/utils/security.py", "hash": "822232487133557567"}, {"file": "apps/backend-api/celerybeat-schedule", "hash": "1223972855955478076"}, {"file": "apps/backend-api/celerybeat-schedule-shm", "hash": "14925781470286555632"}, {"file": "apps/backend-api/celerybeat-schedule-wal", "hash": "14800457933930574320"}, {"file": "apps/backend-api/create_demo_user.py", "hash": "1929378992874166194"}, {"file": "apps/backend-api/debug_sessions.py", "hash": "4474921169229876711"}, {"file": "apps/backend-api/docs/DEVELOPMENT_STANDARDS.md", "hash": "14532089629818650297"}, {"file": "apps/backend-api/docs/FILE_PROCESSING_IMPLEMENTATION.md", "hash": "5769355183009081089"}, {"file": "apps/backend-api/docs/GOOGLE_SEARCH_SETUP.md", "hash": "907603680777050265"}, {"file": "apps/backend-api/docs/PDF_PROCESSING_FIX.md", "hash": "14431347698340861293"}, {"file": "apps/backend-api/fix_integration_permissions.py", "hash": "14200759230479907242"}, {"file": "apps/backend-api/project.json", "hash": "15776168066986678026"}, {"file": "apps/backend-api/pytest.ini", "hash": "3585287711390049353"}, {"file": "apps/backend-api/requirements.txt", "hash": "3721638813930326237"}, {"file": "apps/backend-api/scripts/migrate_to_new_system.py", "hash": "1520747947509474614"}, {"file": "apps/backend-api/temp-nx/aggie/.gitignore", "hash": "18435103667633526488"}, {"file": "apps/backend-api/temp-nx/aggie/README.md", "hash": "5765815396896190387"}, {"file": "apps/backend-api/temp-nx/aggie/nx.json", "hash": "7984885312398650396"}, {"file": "apps/backend-api/temp-nx/aggie/package.json", "hash": "1880928286645203938"}, {"file": "apps/backend-api/temp-nx/aggie/packages/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/backend-api/test_session_creation.py", "hash": "11351597716161844847"}, {"file": "apps/backend-api/test_session_recovery.py", "hash": "2641506352043223522"}, {"file": "apps/backend-api/tests/__init__.py", "hash": "10530555391305414344"}, {"file": "apps/backend-api/tests/conftest.py", "hash": "1305389926311210674"}, {"file": "apps/backend-api/tests/test_auth.py", "hash": "18309435454768705847"}, {"file": "apps/backend-api/tests/test_integrations.py", "hash": "17566668324631171930"}, {"file": "apps/backend-api/tests/test_invoices.py", "hash": "9975863405474238176"}, {"file": "apps/backend-api/tests/test_services.py", "hash": "15467179833860906881"}, {"file": "apps/backend-api/trigger_task.py", "hash": "12209925406597103968"}, {"file": "apps/backend-api/update_permissions.py", "hash": "8380590866922075405"}], "app-frontend": [{"file": "apps/app-frontend/Dockerfile", "hash": "13945634355056200228"}, {"file": "apps/app-frontend/Dockerfile.prod", "hash": "8581775582639739374"}, {"file": "apps/app-frontend/nginx.conf", "hash": "2486793215894162773"}, {"file": "apps/app-frontend/package-lock.json", "hash": "7684299458499068964"}, {"file": "apps/app-frontend/package.json", "hash": "17015359243173538129", "deps": ["npm:@types/qrcode.react", "ui-components", "shared-types", "npm:@types/node@20.19.9", "npm:@types/react", "npm:@types/react-dom", "npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:react-router-dom", "npm:react-scripts", "npm:typescript@4.9.5", "npm:axios", "npm:react-hook-form", "npm:react-query", "npm:@headlessui/react", "npm:@heroicons/react", "npm:tailwindcss", "npm:autoprefixer", "npm:postcss", "npm:qrcode.react", "npm:react-hot-toast", "npm:@testing-library/jest-dom", "npm:@testing-library/react", "npm:@testing-library/user-event", "npm:@tailwindcss/forms"]}, {"file": "apps/app-frontend/postcss.config.js", "hash": "1579725330904862112"}, {"file": "apps/app-frontend/project.json", "hash": "3540457525021900959"}, {"file": "apps/app-frontend/public/index.html", "hash": "17879939718833079058"}, {"file": "apps/app-frontend/src/App.tsx", "hash": "8155976408624883088", "deps": ["npm:react", "npm:react-router-dom", "npm:react-query", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/SimpleApp.tsx", "hash": "4237033659271321490", "deps": ["npm:react"]}, {"file": "apps/app-frontend/src/__tests__/AuthContext.test.tsx", "hash": "12040871057496173520", "deps": ["npm:react"]}, {"file": "apps/app-frontend/src/__tests__/LoginPage.test.tsx", "hash": "13350870590789170105", "deps": ["npm:react"]}, {"file": "apps/app-frontend/src/appCustomStyles.css", "hash": "11401625481371986154"}, {"file": "apps/app-frontend/src/components/IntegrationSettings.tsx", "hash": "10822895034840701375", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/components/Layout.tsx", "hash": "15501166349246846188", "deps": ["npm:react", "npm:react-router-dom", "npm:@headlessui/react", "npm:@heroicons/react"]}, {"file": "apps/app-frontend/src/components/ManualSync.tsx", "hash": "18382046299544396372", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/components/ScheduleSettings.tsx", "hash": "530514965738081598", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/components/settings/CompanySettings.tsx", "hash": "1772728051716481716", "deps": ["npm:react", "npm:@heroicons/react", "npm:react-query", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/contexts/AuthContext.tsx", "hash": "9722001446352333396", "deps": ["npm:react@18.3.1", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/contexts/TenantContext.tsx", "hash": "9708045447128908387", "deps": ["npm:react@18.3.1"]}, {"file": "apps/app-frontend/src/hooks/usePermissions.ts", "hash": "6659383614553137586"}, {"file": "apps/app-frontend/src/index.css", "hash": "6060890077207927115"}, {"file": "apps/app-frontend/src/index.tsx", "hash": "12967885175928185568", "deps": ["npm:react", "npm:react-dom"]}, {"file": "apps/app-frontend/src/pages/ActionItemDetailsPage.tsx", "hash": "2720498748767394986", "deps": ["npm:react", "npm:react-router-dom", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/pages/ActionItemsPage.tsx", "hash": "17017301866789994444", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast", "npm:react-router-dom"]}, {"file": "apps/app-frontend/src/pages/DashboardPage.tsx", "hash": "3529860969623770398", "deps": ["npm:react", "npm:react-query", "npm:react-router-dom", "npm:@heroicons/react"]}, {"file": "apps/app-frontend/src/pages/InvoicesPage.tsx", "hash": "11875118527942069421", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/pages/LoginPage.tsx", "hash": "12579064920638347477", "deps": ["npm:react", "npm:react-router-dom", "npm:react-hook-form", "npm:@heroicons/react"]}, {"file": "apps/app-frontend/src/pages/SessionDetailPage.tsx", "hash": "6624071667059847521", "deps": ["npm:react", "npm:react-router-dom", "npm:react-query", "npm:react-hot-toast", "npm:@heroicons/react"]}, {"file": "apps/app-frontend/src/pages/SessionsPage.tsx", "hash": "2198589526340662545", "deps": ["npm:react", "npm:react-query", "npm:react-router-dom", "npm:react-hot-toast", "npm:@heroicons/react"]}, {"file": "apps/app-frontend/src/pages/SettingsPage.tsx", "hash": "1708412683270142794", "deps": ["npm:react", "npm:react-query", "npm:@heroicons/react", "npm:qrcode.react", "npm:react-hot-toast"]}, {"file": "apps/app-frontend/src/pages/TwoFAVerifyPage.tsx", "hash": "3417118991396930122", "deps": ["npm:react", "npm:react-router-dom", "npm:react-hook-form"]}, {"file": "apps/app-frontend/src/services/api.ts", "hash": "2409508440141427449", "deps": ["npm:axios"]}, {"file": "apps/app-frontend/src/setupTests.ts", "hash": "16903885983588411080"}, {"file": "apps/app-frontend/src/types/index.ts", "hash": "10013640501868491116"}, {"file": "apps/app-frontend/tailwind.config.js", "hash": "216367631106278632"}, {"file": "apps/app-frontend/tsconfig.app.json", "hash": "17310363877181359515"}, {"file": "apps/app-frontend/tsconfig.json", "hash": "6764713495435129772"}]}}}